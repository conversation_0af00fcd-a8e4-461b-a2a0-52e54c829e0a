读取环境变量立即执行优惠券为：heihei
未配置自定义API！
读取环境变量成功：1,3,1,20,20,300

2025-09-07 09:59:33:665:----脚本运行成功，请不要关闭窗口----

2025-09-07 09:59:33:666:----开始运行脚本----
共检测到1个cookie
下次抢券时间：10:00:00
立即抢券（跑完记得删除或禁用该环境变量）：heihei


***开始领券【heihei】第1次请求***
0分后开始任务，请不要结束任务！


1、2025-09-07 09:59:33:703:开始领取heihei_账号1
"getaddrinfo ENOTFOUND api.m.jd.com"
API请求失败，请检查网络重试
Error: Error: getaddrinfo ENOTFOUND cactus.jd.com
    at Object.dispatchError (D:\jdpy\jdlq\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:62:19)
    at Request.<anonymous> (D:\jdpy\jdlq\node_modules\jsdom\lib\jsdom\living\xhr\XMLHttpRequest-impl.js:655:18)
    at Request.emit (node:events:530:35)
    at ClientRequest.<anonymous> (D:\jdpy\jdlq\node_modules\jsdom\lib\jsdom\living\helpers\http-request.js:127:14)
    at ClientRequest.emit (node:events:518:28)
    at emitErrorEvent (node:_http_client:104:11)
    at TLSSocket.socketErrorListener (node:_http_client:518:5)
    at TLSSocket.emit (node:events:518:28)
    at emitErrorNT (node:internal/streams/destroy:170:8)
    at emitErrorCloseNT (node:internal/streams/destroy:129:3) undefined


***开始领券【heihei】第1次请求***


2、2025-09-07 09:59:59:710:开始领取heihei_账号1
"getaddrinfo ENOTFOUND api.m.jd.com"
API请求失败，请检查网络重试
Error: Error: getaddrinfo ENOTFOUND cactus.jd.com
    at Object.dispatchError (D:\jdpy\jdlq\node_modules\jsdom\lib\jsdom\living\xhr\xhr-utils.js:62:19)
    at Request.<anonymous> (D:\jdpy\jdlq\node_modules\jsdom\lib\jsdom\living\xhr\XMLHttpRequest-impl.js:655:18)
    at Request.emit (node:events:530:35)
    at ClientRequest.<anonymous> (D:\jdpy\jdlq\node_modules\jsdom\lib\jsdom\living\helpers\http-request.js:127:14)
    at ClientRequest.emit (node:events:518:28)
    at emitErrorEvent (node:_http_client:104:11)
    at TLSSocket.socketErrorListener (node:_http_client:518:5)
    at TLSSocket.emit (node:events:518:28)
    at emitErrorNT (node:internal/streams/destroy:170:8)
    at emitErrorCloseNT (node:internal/streams/destroy:129:3) undefined
读取环境变量立即执行优惠券为：heihei
未配置自定义API！
读取环境变量成功：1,3,1,20,20,300

2025-09-07 14:59:33:284:----脚本运行成功，请不要关闭窗口----

2025-09-07 14:59:33:285:----开始运行脚本----
共检测到1个cookie
下次抢券时间：15:00:00
立即抢券（跑完记得删除或禁用该环境变量）：heihei


***开始领券【heihei】第1次请求***
0分后开始任务，请不要结束任务！


1、2025-09-07 14:59:33:320:开始领取heihei_账号1


*heihei_【账号1】zhq115*
2025-09-07 14:59:33:654:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


***开始领券【heihei】第1次请求***


2、2025-09-07 14:59:59:727:开始领取heihei_账号1


*heihei_【账号1】zhq115*
2025-09-07 14:59:59:905:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}
读取环境变量立即执行优惠券为：heihei
未配置自定义API！
读取环境变量成功：1,3,1,20,20,300

2025-09-07 17:59:32:590:----脚本运行成功，请不要关闭窗口----

2025-09-07 17:59:32:591:----开始运行脚本----
共检测到1个cookie
下次抢券时间：18:00:00
立即抢券（跑完记得删除或禁用该环境变量）：heihei


***开始领券【heihei】第1次请求***
0分后开始任务，请不要结束任务！


1、2025-09-07 17:59:32:628:开始领取heihei_账号1


*heihei_【账号1】zhq115*
2025-09-07 17:59:32:994:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


***开始领券【heihei】第1次请求***


2、2025-09-07 17:59:59:717:开始领取heihei_账号1


*heihei_【账号1】zhq115*
2025-09-07 17:59:59:923:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}
读取环境变量立即执行优惠券为：heihei
未配置自定义API！
读取环境变量成功：1,3,1,20,20,300

2025-09-07 19:59:32:876:----脚本运行成功，请不要关闭窗口----

2025-09-07 19:59:32:877:----开始运行脚本----
共检测到1个cookie
下次抢券时间：20:00:00
立即抢券（跑完记得删除或禁用该环境变量）：heihei


***开始领券【heihei】第1次请求***
0分后开始任务，请不要结束任务！


1、2025-09-07 19:59:32:916:开始领取heihei_账号1


*heihei_【账号1】zhq115*
2025-09-07 19:59:33:308:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


***开始领券【heihei】第1次请求***


2、2025-09-07 19:59:59:722:开始领取heihei_账号1


*heihei_【账号1】zhq115*
2025-09-07 19:59:59:919:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}
