未配置自定义API！
读取环境变量成功：1,3,5,20,20,210

2025-09-05 09:59:42:347:----脚本运行成功，请不要关闭窗口----

2025-09-05 09:59:42:348:----开始运行脚本----
共检测到5个cookie
下次抢券时间：10:00:00
当前时间段没有优惠券需要领取！

2025-09-05 10:00:00:27:未到任务执行时间，跳过执行......
读取环境变量立即执行优惠券为：医疗膨胀20号网页不行
未配置自定义API！
读取环境变量成功：1,3,1,20,20,210

2025-09-05 14:09:48:587:----脚本运行成功，请不要关闭窗口----

2025-09-05 14:09:48:587:----开始运行脚本----
共检测到1个cookie
下次抢券时间：15:00:00
立即抢券（跑完记得删除或禁用该环境变量）：医疗膨胀20号网页不行


***开始领券【医疗膨胀20号网页不行】第1次请求***
50分后才开始！


1、2025-09-05 14:09:48:612:开始领取医疗膨胀20号网页不行_账号1


*医疗膨胀20号网页不行_【账号1】armishi*
2025-09-05 14:09:49:108:{"msg":"当前所领取优惠券领取次数已达到上限","code":2011,"success":false,"requestId":"5579071.70686.17570525897952039","class":"com.jd.hc.core.user.color.dto.ResultDTO"}
读取环境变量立即执行优惠券为：话费
加载API:话费
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：1,3,1,20,20,210

2025-09-05 14:56:34:488:----脚本运行成功，请不要关闭窗口----

2025-09-05 14:56:34:488:----开始运行脚本----
共检测到1个cookie
下次抢券时间：15:00:00
立即抢券（跑完记得删除或禁用该环境变量）：话费


1、2025-09-05 14:56:34:490:开始领取话费_账号1
3分后才开始！


*话费_【账号1】jd_614aa177acd06*
2025-09-05 14:56:34:804:时间未到继续：本时段优惠券已抢完，请15:00再来吧！
读取环境变量立即执行优惠券为：话费
加载API:话费
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：1,3,1,20,20,210

2025-09-05 14:56:59:766:----脚本运行成功，请不要关闭窗口----

2025-09-05 14:56:59:767:----开始运行脚本----
共检测到1个cookie
下次抢券时间：15:00:00
立即抢券（跑完记得删除或禁用该环境变量）：话费


1、2025-09-05 14:56:59:769:开始领取话费_账号1
2分后才开始！


*话费_【账号1】jd_UpJHStdVIoZe*
2025-09-05 14:57:00:99:时间未到继续：本时段优惠券已抢完，请15:00再来吧！
未配置自定义API！
读取环境变量成功：2,3,1,20,20,250

2025-09-05 14:59:41:840:----脚本运行成功，请不要关闭窗口----

2025-09-05 14:59:41:840:----开始运行脚本----
共检测到1个cookie
下次抢券时间：15:00:00
名称：话费
0分后开始任务，请不要结束任务！


***开始领券【话费】第1次请求***


1、2025-09-05 14:59:59:779:开始领取话费_账号1


***开始领券【话费】第2次请求***


2、2025-09-05 14:59:59:904:开始领取话费_账号1


*话费_【账号1】jd_UpJHStdVIoZe*
2025-09-05 15:00:00:83:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号1】jd_UpJHStdVIoZe*
2025-09-05 15:00:00:84:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}
加载API:话费
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：1,3,1,20,20,250

2025-09-05 15:01:15:173:----脚本运行成功，请不要关闭窗口----

2025-09-05 15:01:15:174:----开始运行脚本----
共检测到1个cookie
下次抢券时间：16:00:00
当前时间段没有优惠券需要领取！
读取环境变量立即执行优惠券为：话费
加载API:话费
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：1,3,1,20,20,250

2025-09-05 15:01:22:610:----脚本运行成功，请不要关闭窗口----

2025-09-05 15:01:22:611:----开始运行脚本----
共检测到1个cookie
下次抢券时间：16:00:00
立即抢券（跑完记得删除或禁用该环境变量）：话费


1、2025-09-05 15:01:22:613:开始领取话费_账号1
58分后才开始！


*话费_【账号1】jd_UpJHStdVIoZe*
2025-09-05 15:01:22:914:本时段优惠券已抢完，请18:00再来吧！
读取环境变量立即执行优惠券为：话费
加载API:话费
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：1,3,1,20,20,250

2025-09-05 15:48:30:579:----脚本运行成功，请不要关闭窗口----

2025-09-05 15:48:30:580:----开始运行脚本----
共检测到1个cookie
下次抢券时间：16:00:00
立即抢券（跑完记得删除或禁用该环境变量）：话费


1、2025-09-05 15:48:30:582:开始领取话费_账号1
11分后才开始！
读取环境变量立即执行优惠券为：话费
加载API:话费
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：1,3,1,20,20,250

2025-09-05 15:48:48:650:----脚本运行成功，请不要关闭窗口----

2025-09-05 15:48:48:651:----开始运行脚本----
共检测到1个cookie
下次抢券时间：16:00:00
立即抢券（跑完记得删除或禁用该环境变量）：话费


1、2025-09-05 15:48:48:653:开始领取话费_账号1
11分后才开始！
读取环境变量立即执行优惠券为：话费
加载API:话费
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：1,3,1,20,20,250

2025-09-05 15:49:34:460:----脚本运行成功，请不要关闭窗口----

2025-09-05 15:49:34:461:----开始运行脚本----
共检测到1个cookie
下次抢券时间：16:00:00
立即抢券（跑完记得删除或禁用该环境变量）：话费


1、2025-09-05 15:49:34:463:开始领取话费_账号1
10分后才开始！


*话费_【账号1】jd_UpJHStdVIoZe*
2025-09-05 15:49:34:798:此券今日已经被抢完，请您明日再来~
读取环境变量立即执行优惠券为：话费
加载API:话费
未配置自定义API！
未设置代理，将不使用代理访问
读取环境变量成功：1,3,1,20,20,250

2025-09-05 15:50:14:261:----脚本运行成功，请不要关闭窗口----

2025-09-05 15:50:14:262:----开始运行脚本----
共检测到5个cookie
下次抢券时间：16:00:00
立即抢券（跑完记得删除或禁用该环境变量）：话费


1、2025-09-05 15:50:14:264:开始领取话费_账号1
9分后才开始！


2、2025-09-05 15:50:14:404:开始领取话费_账号2


3、2025-09-05 15:50:14:428:开始领取话费_账号3


4、2025-09-05 15:50:14:459:开始领取话费_账号4


5、2025-09-05 15:50:14:491:开始领取话费_账号5


*话费_【账号1】zhq115*
2025-09-05 15:50:14:573:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号2】jd_UpJHStdVIoZe*
2025-09-05 15:50:14:586:此券今日已经被抢完，请您明日再来~


*话费_【账号3】jd_614aa177acd06*
2025-09-05 15:50:14:596:此券今日已经被抢完，请您明日再来~


*话费_【账号4】jd_vSVZIXuMHVxW*
2025-09-05 15:50:14:616:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号5】jd_51b29ca7e9a2b*
2025-09-05 15:50:14:653:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}
未配置自定义API！
读取环境变量成功：2,3,1,20,20,250

2025-09-05 17:59:42:285:----脚本运行成功，请不要关闭窗口----

2025-09-05 17:59:42:286:----开始运行脚本----
共检测到1个cookie
下次抢券时间：18:00:00
名称：话费
0分后开始任务，请不要结束任务！


***开始领券【话费】第1次请求***


1、2025-09-05 17:59:59:775:开始领取话费_账号1


***开始领券【话费】第2次请求***


2、2025-09-05 18:00:00:273:开始领取话费_账号1


*话费_【账号1】jd_UpJHStdVIoZe*
2025-09-05 18:00:00:509:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号1】jd_UpJHStdVIoZe*
2025-09-05 18:00:00:511:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}
未配置自定义API！
读取环境变量成功：2,3,1,20,20,380

2025-09-05 19:59:42:52:----脚本运行成功，请不要关闭窗口----

2025-09-05 19:59:42:53:----开始运行脚本----
共检测到1个cookie
下次抢券时间：20:00:00
名称：话费
0分后开始任务，请不要结束任务！


***开始领券【话费】第1次请求***


1、2025-09-05 19:59:59:637:开始领取话费_账号1


***开始领券【话费】第2次请求***


2、2025-09-05 19:59:59:793:开始领取话费_账号1


*话费_【账号1】zhq115*
2025-09-05 19:59:59:965:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号1】zhq115*
2025-09-05 19:59:59:965:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}
未配置自定义API！
读取环境变量成功：2,3,1,20,20,380

2025-09-05 21:59:42:71:----脚本运行成功，请不要关闭窗口----

2025-09-05 21:59:42:72:----开始运行脚本----
共检测到1个cookie
下次抢券时间：22:00:00
名称：话费
0分后开始任务，请不要结束任务！


***开始领券【话费】第1次请求***


1、2025-09-05 21:59:59:647:开始领取话费_账号1


***开始领券【话费】第2次请求***


2、2025-09-05 21:59:59:805:开始领取话费_账号1


*话费_【账号1】zhq115*
2025-09-05 21:59:59:971:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}


*话费_【账号1】zhq115*
2025-09-05 21:59:59:971:{"subCodeMsg":"很抱歉，没抢到~","subCode":"A28","code":"0","msg":null}
